#!/usr/bin/env python3
"""
Wind Noise Data Conversion Summary
显示wind_noise_voice到wind_PCM_data转换的详细统计信息
"""

import os
import glob
from pathlib import Path

def analyze_conversion_results():
    """分析转换结果"""
    print("=== Wind Noise Data Conversion Summary ===\n")
    
    # 源目录和目标目录
    wav_dir = "wind_noise_voice"
    pcm_dir = "wind_PCM_data"
    
    # 检查目录存在性
    wav_exists = os.path.exists(wav_dir)
    pcm_exists = os.path.exists(pcm_dir)
    
    print(f"源目录 (WAV): {wav_dir} - {'✓ 存在' if wav_exists else '✗ 不存在'}")
    print(f"目标目录 (PCM): {pcm_dir} - {'✓ 存在' if pcm_exists else '✗ 不存在'}")
    print()
    
    if not wav_exists or not pcm_exists:
        print("错误: 目录不存在，无法进行分析")
        return
    
    # 获取文件列表
    wav_files = sorted(glob.glob(os.path.join(wav_dir, "*.wav")))
    pcm_files = sorted(glob.glob(os.path.join(pcm_dir, "*.pcm")))
    
    print(f"WAV文件数量: {len(wav_files)}")
    print(f"PCM文件数量: {len(pcm_files)}")
    print(f"转换成功率: {len(pcm_files)/len(wav_files)*100:.1f}%" if wav_files else "N/A")
    print()
    
    # 文件大小统计
    if pcm_files:
        pcm_sizes = [os.path.getsize(f) for f in pcm_files]
        total_size = sum(pcm_sizes)
        avg_size = total_size / len(pcm_sizes)
        
        print("=== PCM文件统计 ===")
        print(f"总大小: {total_size / (1024*1024):.2f} MB")
        print(f"平均大小: {avg_size / 1024:.1f} KB")
        print(f"最小文件: {min(pcm_sizes) / 1024:.1f} KB")
        print(f"最大文件: {max(pcm_sizes) / 1024:.1f} KB")
        print()
        
        # 检查文件大小一致性
        unique_sizes = set(pcm_sizes)
        if len(unique_sizes) == 1:
            print("✓ 所有PCM文件大小一致")
        else:
            print(f"⚠ PCM文件大小不一致，有 {len(unique_sizes)} 种不同大小")
        print()
    
    # 文件名模式分析
    print("=== 文件名模式分析 ===")
    patterns = {}
    for pcm_file in pcm_files:
        filename = os.path.basename(pcm_file).replace('.pcm', '')
        
        if filename.startswith('const_'):
            pattern = 'const_*'
        elif filename.startswith('gusts_'):
            pattern = 'gusts_*'
        elif filename.startswith('mic'):
            pattern = 'mic*'
        elif filename.startswith('wind_'):
            pattern = 'wind_*'
        elif '_' in filename:
            parts = filename.split('_')
            if len(parts) >= 2 and parts[0].isdigit():
                pattern = 'XXX_XXX (数字编号)'
            else:
                pattern = f"{parts[0]}_*"
        else:
            pattern = 'other'
        
        patterns[pattern] = patterns.get(pattern, 0) + 1
    
    for pattern, count in sorted(patterns.items()):
        percentage = count / len(pcm_files) * 100
        print(f"  {pattern}: {count} 个文件 ({percentage:.1f}%)")
    print()
    
    # 示例文件展示
    print("=== 示例PCM文件 ===")
    for i, pcm_file in enumerate(pcm_files[:10]):
        filename = os.path.basename(pcm_file)
        file_size = os.path.getsize(pcm_file)
        print(f"  {i+1:2d}. {filename} ({file_size:,} bytes)")
    
    if len(pcm_files) > 10:
        print(f"  ... 还有 {len(pcm_files)-10} 个文件")
    print()
    
    # 转换质量检查
    print("=== 转换质量检查 ===")
    
    # 检查是否有对应的WAV文件
    missing_wav = []
    for pcm_file in pcm_files:
        pcm_name = os.path.basename(pcm_file).replace('.pcm', '')
        wav_file = os.path.join(wav_dir, f"{pcm_name}.wav")
        if not os.path.exists(wav_file):
            missing_wav.append(pcm_name)
    
    if missing_wav:
        print(f"⚠ 有 {len(missing_wav)} 个PCM文件没有对应的WAV文件:")
        for name in missing_wav[:5]:
            print(f"    - {name}")
        if len(missing_wav) > 5:
            print(f"    ... 还有 {len(missing_wav)-5} 个")
    else:
        print("✓ 所有PCM文件都有对应的WAV文件")
    
    # 检查是否有WAV文件没有转换
    missing_pcm = []
    for wav_file in wav_files:
        wav_name = os.path.basename(wav_file).replace('.wav', '')
        pcm_file = os.path.join(pcm_dir, f"{wav_name}.pcm")
        if not os.path.exists(pcm_file):
            missing_pcm.append(wav_name)
    
    if missing_pcm:
        print(f"⚠ 有 {len(missing_pcm)} 个WAV文件没有转换为PCM:")
        for name in missing_pcm[:5]:
            print(f"    - {name}")
        if len(missing_pcm) > 5:
            print(f"    ... 还有 {len(missing_pcm)-5} 个")
    else:
        print("✓ 所有WAV文件都已成功转换为PCM")
    
    print()
    
    # 使用建议
    print("=== 使用建议 ===")
    print("1. PCM文件已准备就绪，可以用于RNNoise训练")
    print("2. 文件格式: 16位PCM，48kHz采样率，单声道")
    print("3. 可以使用prepare_pcm_data.py进行特征提取")
    print("4. 建议备份原始WAV文件")
    print()
    
    if len(pcm_files) == len(wav_files) and len(pcm_files) > 0:
        print("🎉 转换完全成功！所有文件都已正确转换。")
    elif len(pcm_files) > 0:
        print("⚠ 转换部分成功，请检查失败的文件。")
    else:
        print("❌ 转换失败，没有生成PCM文件。")

if __name__ == "__main__":
    analyze_conversion_results()
