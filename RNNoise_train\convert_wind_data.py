#!/usr/bin/env python3
"""
专门用于转换wind_noise_voice数据到PCM格式的脚本
基于prepare_pcm_data.py的PCMDataPreparator类
"""

import os
import sys
import subprocess
import glob
import argparse
import numpy as np
import tqdm
from pathlib import Path

class WindDataConverter:
    """风噪声数据转换器"""
    
    def __init__(self, ffmpeg_path="../env/ffmpeg.exe"):
        self.ffmpeg_path = ffmpeg_path
        
    def wav_to_pcm(self, wav_file, pcm_file, sample_rate=48000):
        """将WAV文件转换为16位PCM格式"""
        cmd = [
            self.ffmpeg_path,
            '-i', wav_file,
            '-f', 's16le',  # 16位小端格式
            '-ar', str(sample_rate),  # 采样率
            '-ac', '1',  # 单声道
            '-y',  # 覆盖输出文件
            pcm_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError as e:
            print(f"转换失败 {wav_file}: {e.stderr}")
            return False
    
    def convert_wind_data(self, wind_wav_dir="wind_noise_voice", wind_pcm_dir="wind_PCM_data", sample_rate=48000):
        """转换wind_noise_voice数据到wind_PCM_data"""
        print(f"\n=== 转换风噪声数据 ===")
        print(f"源目录: {wind_wav_dir}")
        print(f"目标目录: {wind_pcm_dir}")
        print(f"采样率: {sample_rate} Hz")
        
        # 检查源目录是否存在
        if not os.path.exists(wind_wav_dir):
            print(f"错误: 源目录不存在 - {wind_wav_dir}")
            return False
        
        # 创建目标目录
        os.makedirs(wind_pcm_dir, exist_ok=True)
        print(f"创建目标目录: {wind_pcm_dir}")
        
        # 获取所有WAV文件
        wav_files = sorted(glob.glob(os.path.join(wind_wav_dir, "*.wav")))
        print(f"找到 {len(wav_files)} 个WAV文件")
        
        if not wav_files:
            print("错误: 没有找到WAV文件")
            return False
        
        # 转换统计
        success_count = 0
        failed_files = []
        
        # 批量转换
        print("\n开始转换...")
        for wav_file in tqdm.tqdm(wav_files, desc="转换WAV到PCM"):
            # 获取文件名（不含扩展名）
            filename = os.path.splitext(os.path.basename(wav_file))[0]
            pcm_file = os.path.join(wind_pcm_dir, f"{filename}.pcm")
            
            # 转换文件
            if self.wav_to_pcm(wav_file, pcm_file, sample_rate):
                success_count += 1
            else:
                failed_files.append(wav_file)
        
        # 输出结果
        print(f"\n=== 转换完成 ===")
        print(f"成功转换: {success_count}/{len(wav_files)} 个文件")
        print(f"成功率: {success_count/len(wav_files)*100:.1f}%")
        
        if failed_files:
            print(f"\n转换失败的文件 ({len(failed_files)} 个):")
            for failed_file in failed_files:
                print(f"  - {failed_file}")
        
        # 验证输出目录
        pcm_files = glob.glob(os.path.join(wind_pcm_dir, "*.pcm"))
        print(f"\n验证: 目标目录中有 {len(pcm_files)} 个PCM文件")
        
        # 显示一些示例文件
        if pcm_files:
            print("\n示例PCM文件:")
            for i, pcm_file in enumerate(pcm_files[:5]):
                file_size = os.path.getsize(pcm_file)
                print(f"  {i+1}. {os.path.basename(pcm_file)} ({file_size:,} bytes)")
            
            if len(pcm_files) > 5:
                print(f"  ... 还有 {len(pcm_files)-5} 个文件")
        
        return success_count > 0
    
    def check_ffmpeg(self):
        """检查ffmpeg是否可用"""
        try:
            result = subprocess.run([self.ffmpeg_path, '-version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✓ FFmpeg可用: {self.ffmpeg_path}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"✗ FFmpeg不可用: {self.ffmpeg_path}")
            print("请确保ffmpeg路径正确或安装ffmpeg")
            return False
    
    def get_file_info(self, wav_file):
        """获取WAV文件信息"""
        cmd = [
            self.ffmpeg_path,
            '-i', wav_file,
            '-f', 'null',
            '-'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.stderr  # ffmpeg输出信息到stderr
        except subprocess.CalledProcessError:
            return None
    
    def analyze_wind_data(self, wind_wav_dir="wind_noise_voice"):
        """分析wind_noise_voice数据"""
        print(f"\n=== 分析风噪声数据 ===")
        print(f"目录: {wind_wav_dir}")
        
        if not os.path.exists(wind_wav_dir):
            print(f"错误: 目录不存在 - {wind_wav_dir}")
            return
        
        wav_files = sorted(glob.glob(os.path.join(wind_wav_dir, "*.wav")))
        print(f"WAV文件数量: {len(wav_files)}")
        
        if not wav_files:
            print("没有找到WAV文件")
            return
        
        # 分析前几个文件
        print(f"\n分析前5个文件:")
        for i, wav_file in enumerate(wav_files[:5]):
            filename = os.path.basename(wav_file)
            file_size = os.path.getsize(wav_file)
            print(f"  {i+1}. {filename} ({file_size:,} bytes)")
            
            # 获取文件详细信息
            info = self.get_file_info(wav_file)
            if info:
                # 提取关键信息
                lines = info.split('\n')
                for line in lines:
                    if 'Duration:' in line or 'Stream #0:0' in line:
                        print(f"     {line.strip()}")
        
        # 文件类型统计
        print(f"\n文件名模式分析:")
        patterns = {}
        for wav_file in wav_files:
            filename = os.path.basename(wav_file)
            # 提取文件名模式
            if filename.startswith('const_'):
                pattern = 'const_*'
            elif filename.startswith('gusts_'):
                pattern = 'gusts_*'
            elif filename.startswith('mic'):
                pattern = 'mic*'
            elif filename.startswith('wind_'):
                pattern = 'wind_*'
            elif '_' in filename:
                parts = filename.split('_')
                pattern = f"{parts[0]}_*"
            else:
                pattern = 'other'
            
            patterns[pattern] = patterns.get(pattern, 0) + 1
        
        for pattern, count in sorted(patterns.items()):
            print(f"  {pattern}: {count} 个文件")

def main():
    parser = argparse.ArgumentParser(description='转换wind_noise_voice数据到PCM格式')
    parser.add_argument('--wind-wav-dir', default='wind_noise_voice',
                       help='风噪声WAV文件目录 (默认: wind_noise_voice)')
    parser.add_argument('--wind-pcm-dir', default='wind_PCM_data',
                       help='风噪声PCM输出目录 (默认: wind_PCM_data)')
    parser.add_argument('--sample-rate', type=int, default=48000,
                       help='PCM采样率 (默认: 48000)')
    parser.add_argument('--ffmpeg-path', default='../env/ffmpeg.exe',
                       help='FFmpeg可执行文件路径')
    parser.add_argument('--analyze-only', action='store_true',
                       help='仅分析数据，不进行转换')
    
    args = parser.parse_args()
    
    print("=== Wind Noise Data Converter ===")
    print(f"源目录: {args.wind_wav_dir}")
    print(f"目标目录: {args.wind_pcm_dir}")
    print(f"采样率: {args.sample_rate} Hz")
    print(f"FFmpeg路径: {args.ffmpeg_path}")
    
    # 创建转换器
    converter = WindDataConverter(ffmpeg_path=args.ffmpeg_path)
    
    # 检查ffmpeg
    if not converter.check_ffmpeg():
        print("\n请检查ffmpeg安装和路径配置")
        return
    
    # 分析数据
    converter.analyze_wind_data(args.wind_wav_dir)
    
    if args.analyze_only:
        print("\n仅分析模式，跳过转换")
        return
    
    # 执行转换
    success = converter.convert_wind_data(
        args.wind_wav_dir, 
        args.wind_pcm_dir, 
        args.sample_rate
    )
    
    if success:
        print(f"\n✓ 转换完成！PCM文件保存在: {args.wind_pcm_dir}")
        print(f"\n现在可以使用这些PCM文件进行训练或其他处理")
    else:
        print(f"\n✗ 转换失败！")

if __name__ == "__main__":
    main()
