#!/usr/bin/env python3
"""
PCM数据还原测试脚本
测试clean_PCM_data和wind_PCM_data中的PCM文件是否能正确还原为WAV音频
"""

import os
import sys
import subprocess
import glob
import argparse
from pathlib import Path

class PCMRestoreTester:
    """PCM还原测试器"""
    
    def __init__(self, ffmpeg_path="../env/ffmpeg.exe"):
        self.ffmpeg_path = ffmpeg_path
        
    def pcm_to_wav(self, pcm_file, wav_file, sample_rate=48000):
        """将PCM文件转换为WAV格式"""
        cmd = [
            self.ffmpeg_path,
            '-f', 's16le',  # 输入格式：16位小端PCM
            '-ar', str(sample_rate),  # 采样率
            '-ac', '1',  # 单声道
            '-i', pcm_file,  # 输入PCM文件
            '-y',  # 覆盖输出文件
            wav_file  # 输出WAV文件
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return True, result.stderr
        except subprocess.CalledProcessError as e:
            return False, e.stderr
    
    def get_audio_info(self, audio_file):
        """获取音频文件信息"""
        cmd = [
            self.ffmpeg_path,
            '-i', audio_file,
            '-f', 'null',
            '-'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            return result.stderr
        except subprocess.CalledProcessError as e:
            return e.stderr
    
    def test_pcm_directory(self, pcm_dir, output_dir, test_count=2):
        """测试PCM目录中的文件还原"""
        print(f"\n=== 测试 {pcm_dir} ===")
        
        # 检查目录是否存在
        if not os.path.exists(pcm_dir):
            print(f"错误: PCM目录不存在 - {pcm_dir}")
            return False
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        print(f"输出目录: {output_dir}")
        
        # 获取PCM文件列表
        pcm_files = sorted(glob.glob(os.path.join(pcm_dir, "*.pcm")))
        print(f"找到 {len(pcm_files)} 个PCM文件")
        
        if not pcm_files:
            print("没有找到PCM文件")
            return False
        
        # 选择测试文件
        test_files = pcm_files[:test_count]
        print(f"选择 {len(test_files)} 个文件进行测试:")
        
        success_count = 0
        results = []
        
        for i, pcm_file in enumerate(test_files):
            pcm_name = os.path.basename(pcm_file)
            wav_name = pcm_name.replace('.pcm', '_restored.wav')
            wav_file = os.path.join(output_dir, wav_name)
            
            print(f"\n  {i+1}. 测试文件: {pcm_name}")
            
            # 获取PCM文件信息
            pcm_size = os.path.getsize(pcm_file)
            print(f"     PCM大小: {pcm_size:,} bytes ({pcm_size/1024:.1f} KB)")
            
            # 转换PCM到WAV
            success, info = self.pcm_to_wav(pcm_file, wav_file)
            
            if success:
                print(f"     ✓ 转换成功: {wav_name}")
                
                # 获取生成的WAV文件信息
                if os.path.exists(wav_file):
                    wav_size = os.path.getsize(wav_file)
                    print(f"     WAV大小: {wav_size:,} bytes ({wav_size/1024:.1f} KB)")
                    
                    # 获取音频详细信息
                    audio_info = self.get_audio_info(wav_file)
                    duration_info = self.extract_duration(audio_info)
                    if duration_info:
                        print(f"     音频时长: {duration_info}")
                    
                    format_info = self.extract_format_info(audio_info)
                    if format_info:
                        print(f"     音频格式: {format_info}")
                    
                    success_count += 1
                    results.append({
                        'pcm_file': pcm_name,
                        'wav_file': wav_name,
                        'pcm_size': pcm_size,
                        'wav_size': wav_size,
                        'success': True
                    })
                else:
                    print(f"     ✗ WAV文件未生成")
                    results.append({
                        'pcm_file': pcm_name,
                        'success': False,
                        'error': 'WAV文件未生成'
                    })
            else:
                print(f"     ✗ 转换失败: {info}")
                results.append({
                    'pcm_file': pcm_name,
                    'success': False,
                    'error': info
                })
        
        print(f"\n=== {pcm_dir} 测试结果 ===")
        print(f"成功还原: {success_count}/{len(test_files)} 个文件")
        print(f"成功率: {success_count/len(test_files)*100:.1f}%")
        
        return success_count > 0, results
    
    def extract_duration(self, info_text):
        """从ffmpeg输出中提取音频时长"""
        lines = info_text.split('\n')
        for line in lines:
            if 'Duration:' in line:
                # 提取Duration信息
                parts = line.split('Duration:')
                if len(parts) > 1:
                    duration_part = parts[1].split(',')[0].strip()
                    return duration_part
        return None
    
    def extract_format_info(self, info_text):
        """从ffmpeg输出中提取格式信息"""
        lines = info_text.split('\n')
        for line in lines:
            if 'Stream #0:0' in line and 'Audio:' in line:
                # 提取音频流信息
                return line.strip()
        return None
    
    def check_ffmpeg(self):
        """检查ffmpeg是否可用"""
        try:
            result = subprocess.run([self.ffmpeg_path, '-version'], 
                                  capture_output=True, text=True, check=True)
            print(f"✓ FFmpeg可用: {self.ffmpeg_path}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"✗ FFmpeg不可用: {self.ffmpeg_path}")
            print("请确保ffmpeg路径正确或安装ffmpeg")
            return False

def main():
    parser = argparse.ArgumentParser(description='测试PCM数据还原为WAV音频')
    parser.add_argument('--clean-pcm-dir', default='clean_PCM_data',
                       help='清晰语音PCM目录')
    parser.add_argument('--wind-pcm-dir', default='wind_PCM_data',
                       help='风噪声PCM目录')
    parser.add_argument('--output-dir', default='pcm_restore_test',
                       help='还原音频输出目录')
    parser.add_argument('--test-count', type=int, default=2,
                       help='每个目录测试的文件数量')
    parser.add_argument('--sample-rate', type=int, default=48000,
                       help='PCM采样率')
    parser.add_argument('--ffmpeg-path', default='../env/ffmpeg.exe',
                       help='FFmpeg可执行文件路径')
    
    args = parser.parse_args()
    
    print("=== PCM数据还原测试 ===")
    print(f"清晰语音PCM目录: {args.clean_pcm_dir}")
    print(f"风噪声PCM目录: {args.wind_pcm_dir}")
    print(f"输出目录: {args.output_dir}")
    print(f"每目录测试文件数: {args.test_count}")
    print(f"采样率: {args.sample_rate} Hz")
    
    # 创建测试器
    tester = PCMRestoreTester(ffmpeg_path=args.ffmpeg_path)
    
    # 检查ffmpeg
    if not tester.check_ffmpeg():
        print("\n请检查ffmpeg安装和路径配置")
        return
    
    # 创建主输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 测试结果
    all_results = {}
    
    # 测试clean_PCM_data
    if os.path.exists(args.clean_pcm_dir):
        clean_output_dir = os.path.join(args.output_dir, "clean_restored")
        success, results = tester.test_pcm_directory(
            args.clean_pcm_dir, clean_output_dir, args.test_count)
        all_results['clean'] = {'success': success, 'results': results}
    else:
        print(f"\n⚠ 跳过 {args.clean_pcm_dir} (目录不存在)")
    
    # 测试wind_PCM_data
    if os.path.exists(args.wind_pcm_dir):
        wind_output_dir = os.path.join(args.output_dir, "wind_restored")
        success, results = tester.test_pcm_directory(
            args.wind_pcm_dir, wind_output_dir, args.test_count)
        all_results['wind'] = {'success': success, 'results': results}
    else:
        print(f"\n⚠ 跳过 {args.wind_pcm_dir} (目录不存在)")
    
    # 总结
    print(f"\n=== 总体测试结果 ===")
    for test_type, result in all_results.items():
        if result['success']:
            print(f"✓ {test_type}_PCM_data: 还原成功")
        else:
            print(f"✗ {test_type}_PCM_data: 还原失败")
    
    print(f"\n还原的音频文件保存在: {args.output_dir}")
    print("可以使用音频播放器播放这些WAV文件来验证音质")

if __name__ == "__main__":
    main()
