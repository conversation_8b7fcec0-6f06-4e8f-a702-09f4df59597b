#!/usr/bin/env python3
"""
简单音频测试脚本
使用ffmpeg验证还原的音频文件是否可以正常播放和分析
"""

import os
import sys
import subprocess
import glob
import argparse

class SimpleAudioTester:
    """简单音频测试器"""
    
    def __init__(self, ffmpeg_path="../env/ffmpeg.exe"):
        self.ffmpeg_path = ffmpeg_path
    
    def analyze_audio(self, audio_file):
        """分析音频文件"""
        print(f"\n=== 分析音频文件: {os.path.basename(audio_file)} ===")
        
        if not os.path.exists(audio_file):
            print(f"✗ 文件不存在: {audio_file}")
            return False
        
        # 获取文件大小
        file_size = os.path.getsize(audio_file)
        print(f"文件大小: {file_size:,} bytes ({file_size/1024:.1f} KB)")
        
        # 使用ffmpeg分析音频
        cmd = [
            self.ffmpeg_path,
            '-i', audio_file,
            '-f', 'null',
            '-'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            # 解析输出信息
            info_lines = result.stderr.split('\n')
            
            print("音频信息:")
            for line in info_lines:
                if 'Duration:' in line:
                    print(f"  {line.strip()}")
                elif 'Stream #0:0' in line and 'Audio:' in line:
                    print(f"  {line.strip()}")
            
            # 检查是否有错误
            if 'Invalid data found' in result.stderr or 'No such file' in result.stderr:
                print("✗ 音频文件格式错误或损坏")
                return False
            else:
                print("✓ 音频文件格式正确，可以正常解析")
                return True
                
        except subprocess.CalledProcessError as e:
            print(f"✗ 分析失败: {e}")
            return False
    
    def test_audio_conversion(self, audio_file, output_format='mp3'):
        """测试音频转换（验证文件完整性）"""
        if not os.path.exists(audio_file):
            return False
        
        # 生成测试输出文件名
        base_name = os.path.splitext(os.path.basename(audio_file))[0]
        test_output = f"temp_test_{base_name}.{output_format}"
        
        cmd = [
            self.ffmpeg_path,
            '-i', audio_file,
            '-y',  # 覆盖输出文件
            test_output
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            if os.path.exists(test_output):
                test_size = os.path.getsize(test_output)
                print(f"✓ 转换测试成功，生成 {output_format.upper()} 文件: {test_size:,} bytes")
                
                # 清理测试文件
                os.remove(test_output)
                return True
            else:
                print(f"✗ 转换测试失败，未生成输出文件")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"✗ 转换测试失败: {e.stderr}")
            return False
    
    def extract_audio_sample(self, audio_file, duration=1.0):
        """提取音频样本进行测试"""
        if not os.path.exists(audio_file):
            return False
        
        base_name = os.path.splitext(os.path.basename(audio_file))[0]
        sample_output = f"sample_{base_name}.wav"
        
        cmd = [
            self.ffmpeg_path,
            '-i', audio_file,
            '-t', str(duration),  # 提取指定时长
            '-y',
            sample_output
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            
            if os.path.exists(sample_output):
                sample_size = os.path.getsize(sample_output)
                print(f"✓ 音频样本提取成功: {sample_output} ({sample_size:,} bytes)")
                return True
            else:
                print(f"✗ 音频样本提取失败")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"✗ 音频样本提取失败: {e.stderr}")
            return False

def main():
    parser = argparse.ArgumentParser(description='简单音频文件测试')
    parser.add_argument('--test-dir', default='pcm_restore_test',
                       help='测试音频文件目录')
    parser.add_argument('--ffmpeg-path', default='../env/ffmpeg.exe',
                       help='FFmpeg可执行文件路径')
    
    args = parser.parse_args()
    
    print("=== 简单音频文件测试 ===")
    print(f"测试目录: {args.test_dir}")
    
    # 创建测试器
    tester = SimpleAudioTester(ffmpeg_path=args.ffmpeg_path)
    
    # 检查ffmpeg
    try:
        result = subprocess.run([args.ffmpeg_path, '-version'], 
                              capture_output=True, text=True, check=True)
        print(f"✓ FFmpeg可用")
    except:
        print(f"✗ FFmpeg不可用: {args.ffmpeg_path}")
        return
    
    # 查找所有还原的音频文件
    test_files = []
    
    if os.path.exists(args.test_dir):
        # 递归查找所有WAV文件
        for root, dirs, files in os.walk(args.test_dir):
            for file in files:
                if file.endswith('.wav'):
                    test_files.append(os.path.join(root, file))
    
    if not test_files:
        print(f"在 {args.test_dir} 中没有找到WAV文件")
        print("请先运行 test_pcm_restore.py 生成还原的音频文件")
        return
    
    print(f"\n找到 {len(test_files)} 个音频文件进行测试")
    
    # 测试每个音频文件
    success_count = 0
    
    for i, audio_file in enumerate(test_files):
        print(f"\n{'='*60}")
        print(f"测试 {i+1}/{len(test_files)}: {os.path.basename(audio_file)}")
        print(f"完整路径: {audio_file}")
        
        # 基本分析
        analysis_ok = tester.analyze_audio(audio_file)
        
        if analysis_ok:
            # 转换测试
            conversion_ok = tester.test_audio_conversion(audio_file, 'mp3')
            
            # 样本提取测试
            sample_ok = tester.extract_audio_sample(audio_file, 0.5)
            
            if conversion_ok and sample_ok:
                print("✓ 所有测试通过")
                success_count += 1
            else:
                print("⚠ 部分测试失败")
        else:
            print("✗ 基本分析失败")
    
    # 总结
    print(f"\n{'='*60}")
    print(f"=== 测试总结 ===")
    print(f"测试通过: {success_count}/{len(test_files)} 个文件")
    print(f"成功率: {success_count/len(test_files)*100:.1f}%")
    
    if success_count == len(test_files):
        print("\n🎉 所有音频文件测试通过！")
        print("PCM数据已成功还原为可播放的音频文件")
    elif success_count > 0:
        print(f"\n⚠ 部分音频文件测试通过")
        print("大部分PCM数据还原正常")
    else:
        print(f"\n❌ 所有音频文件测试失败")
        print("PCM数据还原可能存在问题")
    
    # 清理样本文件
    sample_files = glob.glob("sample_*.wav")
    if sample_files:
        print(f"\n清理 {len(sample_files)} 个测试样本文件...")
        for sample_file in sample_files:
            try:
                os.remove(sample_file)
            except:
                pass

if __name__ == "__main__":
    main()
