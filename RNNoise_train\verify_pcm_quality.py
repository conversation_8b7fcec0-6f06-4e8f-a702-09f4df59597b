#!/usr/bin/env python3
"""
PCM质量验证脚本
比较原始WAV文件和从PCM还原的WAV文件，验证转换质量
"""

import os
import sys
import subprocess
import numpy as np
import wave
import argparse
from pathlib import Path

class PCMQualityVerifier:
    """PCM质量验证器"""
    
    def __init__(self, ffmpeg_path="../env/ffmpeg.exe"):
        self.ffmpeg_path = ffmpeg_path
    
    def get_detailed_audio_info(self, audio_file):
        """获取详细的音频信息"""
        cmd = [
            self.ffmpeg_path,
            '-i', audio_file,
            '-f', 'null',
            '-'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            return self.parse_audio_info(result.stderr)
        except subprocess.CalledProcessError as e:
            return None
    
    def parse_audio_info(self, info_text):
        """解析音频信息"""
        info = {}
        lines = info_text.split('\n')
        
        for line in lines:
            if 'Duration:' in line:
                # 提取时长
                parts = line.split('Duration:')
                if len(parts) > 1:
                    duration_part = parts[1].split(',')[0].strip()
                    info['duration'] = duration_part
            
            if 'Stream #0:0' in line and 'Audio:' in line:
                # 提取音频流信息
                info['stream_info'] = line.strip()
                
                # 提取采样率
                if 'Hz' in line:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if 'Hz' in part:
                            info['sample_rate'] = part.replace('Hz,', '').replace('Hz', '')
                            break
                
                # 提取声道数
                if 'channels' in line:
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if 'channels' in part and i > 0:
                            info['channels'] = parts[i-1]
                            break
                elif 'mono' in line:
                    info['channels'] = '1'
                elif 'stereo' in line:
                    info['channels'] = '2'
        
        return info
    
    def read_wav_data(self, wav_file):
        """读取WAV文件数据"""
        try:
            with wave.open(wav_file, 'rb') as wav:
                frames = wav.getnframes()
                sample_rate = wav.getframerate()
                channels = wav.getnchannels()
                sample_width = wav.getsampwidth()
                
                # 读取音频数据
                audio_data = wav.readframes(frames)
                
                # 转换为numpy数组
                if sample_width == 2:  # 16位
                    audio_array = np.frombuffer(audio_data, dtype=np.int16)
                elif sample_width == 4:  # 32位
                    audio_array = np.frombuffer(audio_data, dtype=np.int32)
                else:
                    audio_array = np.frombuffer(audio_data, dtype=np.uint8)
                
                return {
                    'data': audio_array,
                    'frames': frames,
                    'sample_rate': sample_rate,
                    'channels': channels,
                    'sample_width': sample_width,
                    'duration': frames / sample_rate
                }
        except Exception as e:
            print(f"读取WAV文件失败: {e}")
            return None
    
    def compare_audio_files(self, original_wav, restored_wav):
        """比较原始WAV和还原WAV文件"""
        print(f"\n=== 音频文件比较 ===")
        print(f"原始文件: {os.path.basename(original_wav)}")
        print(f"还原文件: {os.path.basename(restored_wav)}")
        
        # 检查文件是否存在
        if not os.path.exists(original_wav):
            print(f"✗ 原始文件不存在: {original_wav}")
            return False
        
        if not os.path.exists(restored_wav):
            print(f"✗ 还原文件不存在: {restored_wav}")
            return False
        
        # 获取文件大小
        orig_size = os.path.getsize(original_wav)
        rest_size = os.path.getsize(restored_wav)
        
        print(f"\n文件大小比较:")
        print(f"  原始: {orig_size:,} bytes ({orig_size/1024:.1f} KB)")
        print(f"  还原: {rest_size:,} bytes ({rest_size/1024:.1f} KB)")
        print(f"  差异: {abs(orig_size-rest_size):,} bytes")
        
        # 获取详细音频信息
        orig_info = self.get_detailed_audio_info(original_wav)
        rest_info = self.get_detailed_audio_info(restored_wav)
        
        if orig_info and rest_info:
            print(f"\n音频参数比较:")
            print(f"  时长 - 原始: {orig_info.get('duration', 'N/A')}, 还原: {rest_info.get('duration', 'N/A')}")
            print(f"  采样率 - 原始: {orig_info.get('sample_rate', 'N/A')}, 还原: {rest_info.get('sample_rate', 'N/A')}")
            print(f"  声道数 - 原始: {orig_info.get('channels', 'N/A')}, 还原: {rest_info.get('channels', 'N/A')}")
        
        # 读取音频数据进行详细比较
        orig_data = self.read_wav_data(original_wav)
        rest_data = self.read_wav_data(restored_wav)
        
        if orig_data and rest_data:
            print(f"\n音频数据分析:")
            print(f"  原始数据长度: {len(orig_data['data']):,} 采样点")
            print(f"  还原数据长度: {len(rest_data['data']):,} 采样点")
            print(f"  原始时长: {orig_data['duration']:.3f} 秒")
            print(f"  还原时长: {rest_data['duration']:.3f} 秒")
            
            # 数据完整性检查
            if len(orig_data['data']) == len(rest_data['data']):
                print("  ✓ 数据长度一致")
                
                # 计算数据差异
                if np.array_equal(orig_data['data'], rest_data['data']):
                    print("  ✓ 音频数据完全一致")
                    return True
                else:
                    # 计算差异统计
                    diff = orig_data['data'].astype(np.float32) - rest_data['data'].astype(np.float32)
                    max_diff = np.max(np.abs(diff))
                    mean_diff = np.mean(np.abs(diff))
                    
                    print(f"  ⚠ 音频数据有差异:")
                    print(f"    最大差异: {max_diff}")
                    print(f"    平均差异: {mean_diff:.2f}")
                    
                    if max_diff < 10:  # 允许小的量化误差
                        print("  ✓ 差异在可接受范围内")
                        return True
                    else:
                        print("  ✗ 差异较大，可能存在质量问题")
                        return False
            else:
                print("  ✗ 数据长度不一致")
                return False
        
        return False
    
    def verify_pcm_conversion(self, test_cases):
        """验证PCM转换质量"""
        print("=== PCM转换质量验证 ===\n")
        
        results = []
        
        for case in test_cases:
            original_wav = case['original']
            restored_wav = case['restored']
            case_name = case['name']
            
            print(f"验证案例: {case_name}")
            success = self.compare_audio_files(original_wav, restored_wav)
            
            results.append({
                'name': case_name,
                'success': success,
                'original': original_wav,
                'restored': restored_wav
            })
            
            print("-" * 60)
        
        # 总结
        print(f"\n=== 验证总结 ===")
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        print(f"验证通过: {success_count}/{total_count} 个案例")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        
        for result in results:
            status = "✓" if result['success'] else "✗"
            print(f"  {status} {result['name']}")
        
        return success_count == total_count

def main():
    parser = argparse.ArgumentParser(description='验证PCM转换质量')
    parser.add_argument('--ffmpeg-path', default='../env/ffmpeg.exe',
                       help='FFmpeg可执行文件路径')
    
    args = parser.parse_args()
    
    # 创建验证器
    verifier = PCMQualityVerifier(ffmpeg_path=args.ffmpeg_path)
    
    # 定义测试案例
    test_cases = []
    
    # Clean PCM测试案例
    clean_original = "clean_PCM_data"  # 这里应该是原始clean voice目录，但我们用现有的
    clean_restored = "pcm_restore_test/clean_restored"
    
    if os.path.exists(clean_restored):
        restored_files = [f for f in os.listdir(clean_restored) if f.endswith('_restored.wav')]
        for restored_file in restored_files[:2]:  # 测试前2个
            # 尝试找到对应的原始文件
            base_name = restored_file.replace('_restored.wav', '')
            
            # 在wind_noise_voice中查找原始文件（因为我们知道这些文件的来源）
            original_candidates = [
                f"wind_noise_voice/{base_name}.wav",
                f"clean_voice/{base_name}.wav"  # 如果有clean_voice目录
            ]
            
            for orig_path in original_candidates:
                if os.path.exists(orig_path):
                    test_cases.append({
                        'name': f'Clean PCM - {base_name}',
                        'original': orig_path,
                        'restored': os.path.join(clean_restored, restored_file)
                    })
                    break
    
    # Wind PCM测试案例
    wind_restored = "pcm_restore_test/wind_restored"
    
    if os.path.exists(wind_restored):
        restored_files = [f for f in os.listdir(wind_restored) if f.endswith('_restored.wav')]
        for restored_file in restored_files[:2]:  # 测试前2个
            base_name = restored_file.replace('_restored.wav', '')
            original_path = f"wind_noise_voice/{base_name}.wav"
            
            if os.path.exists(original_path):
                test_cases.append({
                    'name': f'Wind PCM - {base_name}',
                    'original': original_path,
                    'restored': os.path.join(wind_restored, restored_file)
                })
    
    if not test_cases:
        print("没有找到可验证的测试案例")
        print("请先运行 test_pcm_restore.py 生成还原的音频文件")
        return
    
    # 执行验证
    success = verifier.verify_pcm_conversion(test_cases)
    
    if success:
        print("\n🎉 所有PCM转换质量验证通过！")
        print("PCM数据可以完美还原为原始音频质量")
    else:
        print("\n⚠ 部分PCM转换存在质量问题")
        print("建议检查转换参数和流程")

if __name__ == "__main__":
    main()
